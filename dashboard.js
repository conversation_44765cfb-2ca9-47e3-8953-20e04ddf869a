// Dashboard functionality

let currentStep = 1;
let maxSteps = 6;
let channelData = {};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check if user has completed setup
    const hasCompletedSetup = localStorage.getItem('ytflow_setup_completed');
    
    if (hasCompletedSetup) {
        showDashboard();
    } else {
        showWelcome();
    }
    
    // Initialize event listeners
    initializeEventListeners();
});

// Initialize event listeners
function initializeEventListeners() {
    // Category selection
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', () => selectCategory(card));
    });
    
    // Color palette selection
    const colorOptions = document.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', () => selectColor(option));
    });
    
    // Form inputs
    const channelNameInput = document.getElementById('channelName');
    if (channelNameInput) {
        channelNameInput.addEventListener('input', (e) => {
            channelData.name = e.target.value;
        });
    }
    
    const descriptionInput = document.getElementById('channelDescription');
    if (descriptionInput) {
        descriptionInput.addEventListener('input', (e) => {
            channelData.description = e.target.value;
            updateCharacterCount();
        });
    }
    
    // File uploads
    const logoUpload = document.getElementById('logoUpload');
    if (logoUpload) {
        logoUpload.addEventListener('change', handleLogoUpload);
    }
    
    const coverUpload = document.getElementById('coverUpload');
    if (coverUpload) {
        coverUpload.addEventListener('change', handleCoverUpload);
    }
}

// Show welcome section
function showWelcome() {
    document.getElementById('welcomeSection').style.display = 'block';
    document.getElementById('wizardSection').style.display = 'none';
    document.getElementById('importSection').style.display = 'none';
    document.getElementById('dashboardContent').style.display = 'none';
}

// Start new channel wizard
function startNewChannelWizard() {
    document.getElementById('welcomeSection').style.display = 'none';
    document.getElementById('wizardSection').style.display = 'block';
    document.getElementById('importSection').style.display = 'none';
    
    currentStep = 1;
    updateWizardStep();
}

// Start import wizard
function startImportWizard() {
    document.getElementById('welcomeSection').style.display = 'none';
    document.getElementById('wizardSection').style.display = 'none';
    document.getElementById('importSection').style.display = 'block';
}

// Update wizard step
function updateWizardStep() {
    // Update progress indicators
    const progressSteps = document.querySelectorAll('.progress-step');
    progressSteps.forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed');
        
        if (stepNumber < currentStep) {
            step.classList.add('completed');
        } else if (stepNumber === currentStep) {
            step.classList.add('active');
        }
    });
    
    // Show current step content
    const wizardSteps = document.querySelectorAll('.wizard-step');
    wizardSteps.forEach((step, index) => {
        step.classList.remove('active');
        if (index + 1 === currentStep) {
            step.classList.add('active');
        }
    });
    
    // Update navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const finishBtn = document.getElementById('finishBtn');
    
    prevBtn.style.display = currentStep > 1 ? 'inline-flex' : 'none';
    nextBtn.style.display = currentStep < maxSteps ? 'inline-flex' : 'none';
    finishBtn.style.display = currentStep === maxSteps ? 'inline-flex' : 'none';
}

// Navigate to next step
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < maxSteps) {
            currentStep++;
            updateWizardStep();
        }
    }
}

// Navigate to previous step
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateWizardStep();
    }
}

// Validate current step
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return validateCategoryStep();
        case 2:
            return validateRegionStep();
        case 3:
            return validateChannelDetailsStep();
        case 4:
            return true; // Branding is optional
        case 5:
            return true; // Competitors are optional
        case 6:
            return true; // Strategy is optional
        default:
            return true;
    }
}

// Validate category step
function validateCategoryStep() {
    const selectedCategory = document.querySelector('.category-card.selected');
    if (!selectedCategory) {
        alert('Please select a content category to continue.');
        return false;
    }
    channelData.category = selectedCategory.dataset.category;
    return true;
}

// Validate region step
function validateRegionStep() {
    const region = document.getElementById('region').value;
    const language = document.getElementById('language').value;
    
    if (!region || !language) {
        alert('Please select both region and language to continue.');
        return false;
    }
    
    channelData.region = region;
    channelData.language = language;
    return true;
}

// Validate channel details step
function validateChannelDetailsStep() {
    const channelName = document.getElementById('channelName').value.trim();
    
    if (!channelName) {
        alert('Please enter a channel name to continue.');
        return false;
    }
    
    channelData.name = channelName;
    channelData.description = document.getElementById('channelDescription').value;
    channelData.keywords = document.getElementById('channelKeywords').value;
    return true;
}

// Select category
function selectCategory(card) {
    // Remove previous selection
    document.querySelectorAll('.category-card').forEach(c => c.classList.remove('selected'));
    
    // Select current card
    card.classList.add('selected');
    
    // Store selection
    channelData.category = card.dataset.category;
}

// Select color
function selectColor(option) {
    // Remove previous selection
    document.querySelectorAll('.color-option').forEach(c => c.classList.remove('selected'));
    
    // Select current option
    option.classList.add('selected');
    
    // Store selection
    channelData.brandColor = option.dataset.color;
}

// Generate channel names
function generateChannelNames() {
    const category = channelData.category || 'general';
    const suggestions = getChannelNameSuggestions(category);
    
    showAISuggestions('channelNameSuggestions', suggestions, (suggestion) => {
        document.getElementById('channelName').value = suggestion;
        channelData.name = suggestion;
    });
}

// Generate channel description
function generateChannelDescription() {
    const category = channelData.category || 'general';
    const name = channelData.name || 'Your Channel';
    
    const description = generateDescriptionText(category, name);
    document.getElementById('channelDescription').value = description;
    channelData.description = description;
    updateCharacterCount();
}

// Generate keywords
function generateKeywords() {
    const category = channelData.category || 'general';
    const keywords = getKeywordSuggestions(category);
    
    showAISuggestions('keywordSuggestions', keywords, (suggestion) => {
        const currentKeywords = document.getElementById('channelKeywords').value;
        const newKeywords = currentKeywords ? currentKeywords + ', ' + suggestion : suggestion;
        document.getElementById('channelKeywords').value = newKeywords;
        channelData.keywords = newKeywords;
    });
}

// Show AI suggestions
function showAISuggestions(containerId, suggestions, onSelect) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.textContent = suggestion;
        item.onclick = () => {
            onSelect(suggestion);
            container.style.display = 'none';
        };
        container.appendChild(item);
    });
    
    container.classList.add('show');
}

// Update character count
function updateCharacterCount() {
    const description = document.getElementById('channelDescription').value;
    const count = document.getElementById('descriptionCount');
    if (count) {
        count.textContent = description.length;
        count.style.color = description.length > 1000 ? '#ef4444' : '#666';
    }
}

// Handle logo upload
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.getElementById('logoPreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
            channelData.logo = e.target.result;
        };
        reader.readAsDataURL(file);
    }
}

// Handle cover upload
function handleCoverUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.getElementById('coverPreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Cover" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
            channelData.cover = e.target.result;
        };
        reader.readAsDataURL(file);
    }
}

// Upload logo
function uploadLogo() {
    document.getElementById('logoUpload').click();
}

// Upload cover
function uploadCover() {
    document.getElementById('coverUpload').click();
}

// Generate logo with AI
function generateLogo() {
    const category = channelData.category || 'general';
    const name = channelData.name || 'Channel';
    
    // Simulate AI logo generation
    const logoUrl = `https://via.placeholder.com/200x200/ff0000/ffffff?text=${encodeURIComponent(name.charAt(0))}`;
    const preview = document.getElementById('logoPreview');
    preview.innerHTML = `<img src="${logoUrl}" alt="Generated Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
    channelData.logo = logoUrl;
    
    // Show success message
    alert('AI logo generated! You can upload a custom logo or generate a new one.');
}

// Generate cover with AI
function generateCover() {
    const category = channelData.category || 'general';
    const name = channelData.name || 'Channel';
    
    // Simulate AI cover generation
    const coverUrl = `https://via.placeholder.com/2560x1440/ff0000/ffffff?text=${encodeURIComponent(name)}`;
    const preview = document.getElementById('coverPreview');
    preview.innerHTML = `<img src="${coverUrl}" alt="Generated Cover" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
    channelData.cover = coverUrl;
    
    // Show success message
    alert('AI cover image generated! You can upload a custom cover or generate a new one.');
}

// Search competitors
function searchCompetitors() {
    const query = document.getElementById('competitorSearch').value.trim();
    if (!query) return;
    
    // Simulate competitor search
    const results = [
        {
            name: query + ' Channel 1',
            subscribers: '1.2M',
            avatar: 'https://via.placeholder.com/50',
            description: 'Popular channel in your niche'
        },
        {
            name: query + ' Channel 2',
            subscribers: '890K',
            avatar: 'https://via.placeholder.com/50',
            description: 'Growing competitor channel'
        }
    ];
    
    displayCompetitorResults(results);
}

// Suggest competitors with AI
function suggestCompetitors() {
    const category = channelData.category || 'general';
    
    // Simulate AI competitor suggestions
    const suggestions = getCompetitorSuggestions(category);
    displayCompetitorResults(suggestions);
}

// Display competitor results
function displayCompetitorResults(competitors) {
    const container = document.getElementById('competitorResults');
    container.innerHTML = '';
    
    competitors.forEach(competitor => {
        const card = document.createElement('div');
        card.className = 'competitor-card';
        card.innerHTML = `
            <div class="competitor-info">
                <img src="${competitor.avatar}" alt="${competitor.name}" class="competitor-avatar">
                <div class="competitor-details">
                    <h4>${competitor.name}</h4>
                    <p>${competitor.subscribers} subscribers • ${competitor.description}</p>
                </div>
            </div>
            <button class="add-competitor-btn" onclick="addCompetitor('${competitor.name}', '${competitor.avatar}', '${competitor.subscribers}')">
                <i class="fas fa-plus"></i> Add
            </button>
        `;
        container.appendChild(card);
    });
}

// Add competitor
function addCompetitor(name, avatar, subscribers) {
    if (!channelData.competitors) {
        channelData.competitors = [];
    }
    
    // Check if already added
    if (channelData.competitors.find(c => c.name === name)) {
        alert('This competitor is already added.');
        return;
    }
    
    channelData.competitors.push({ name, avatar, subscribers });
    updateSelectedCompetitors();
}

// Update selected competitors display
function updateSelectedCompetitors() {
    const container = document.getElementById('selectedCompetitors');
    
    if (!channelData.competitors || channelData.competitors.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <p>No competitors selected yet. Use the search above or AI suggestions.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = channelData.competitors.map(competitor => `
        <div class="competitor-card">
            <div class="competitor-info">
                <img src="${competitor.avatar}" alt="${competitor.name}" class="competitor-avatar">
                <div class="competitor-details">
                    <h4>${competitor.name}</h4>
                    <p>${competitor.subscribers} subscribers</p>
                </div>
            </div>
            <button class="btn btn-outline" onclick="removeCompetitor('${competitor.name}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

// Remove competitor
function removeCompetitor(name) {
    if (channelData.competitors) {
        channelData.competitors = channelData.competitors.filter(c => c.name !== name);
        updateSelectedCompetitors();
    }
}

// Generate description template
function generateDescriptionTemplate() {
    const category = channelData.category || 'general';
    const template = getDescriptionTemplate(category);
    
    document.getElementById('descriptionTemplate').value = template;
    channelData.descriptionTemplate = template;
}

// Finish setup
function finishSetup() {
    // Collect final data
    const scheduleInputs = document.querySelectorAll('input[name="schedule"]:checked');
    channelData.postingSchedule = Array.from(scheduleInputs).map(input => input.value);
    
    channelData.descriptionTemplate = document.getElementById('descriptionTemplate').value;
    
    // Save to localStorage
    localStorage.setItem('ytflow_channel_data', JSON.stringify(channelData));
    localStorage.setItem('ytflow_setup_completed', 'true');
    
    // Show success and redirect to dashboard
    alert('Channel setup completed successfully! Welcome to YTFlow.');
    showDashboard();
}

// Show dashboard
function showDashboard() {
    document.getElementById('welcomeSection').style.display = 'none';
    document.getElementById('wizardSection').style.display = 'none';
    document.getElementById('importSection').style.display = 'none';
    document.getElementById('dashboardContent').style.display = 'block';
    
    // Load dashboard content
    loadDashboardContent();
}

// Load dashboard content
function loadDashboardContent() {
    const container = document.getElementById('dashboardContent');
    const channelData = JSON.parse(localStorage.getItem('ytflow_channel_data') || '{}');
    
    container.innerHTML = `
        <div class="dashboard-header">
            <h1>Welcome back, ${channelData.name || 'Creator'}!</h1>
            <p>Here's your channel overview and latest insights.</p>
        </div>
        
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <h3>Channel Setup Complete</h3>
                <p>Your channel is ready to go! Start creating amazing content.</p>
                <button class="btn btn-primary">Create First Video</button>
            </div>
            
            <div class="dashboard-card">
                <h3>AI Content Ideas</h3>
                <p>Get personalized content suggestions based on your niche.</p>
                <button class="btn btn-outline">Generate Ideas</button>
            </div>
        </div>
    `;
}

// Import by URL
function importByUrl() {
    const url = document.getElementById('channelUrl').value.trim();
    if (!url) {
        alert('Please enter a valid YouTube channel URL.');
        return;
    }
    
    // Simulate import process
    alert('Importing channel data... This feature will be available soon!');
}

// Connect YouTube
function connectYouTube() {
    // Simulate OAuth flow
    alert('Connecting to YouTube... This feature will be available soon!');
}

// Toggle user menu
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userMenu.contains(event.target)) {
        dropdown.classList.remove('show');
    }
});

// Helper functions for AI suggestions
function getChannelNameSuggestions(category) {
    const suggestions = {
        tech: ['TechFlow Pro', 'Digital Insights Hub', 'Innovation Station', 'Tech Trends Today'],
        gaming: ['GameMaster Central', 'Epic Gaming Hub', 'Pro Gamer Zone', 'Gaming Universe'],
        lifestyle: ['Life & Style Co', 'Daily Vibes', 'Lifestyle Luxe', 'Modern Living'],
        education: ['Learn & Grow', 'Knowledge Hub', 'EduFlow', 'Smart Learning'],
        entertainment: ['Entertainment Central', 'Fun Times TV', 'Show & Tell', 'Entertainment Plus'],
        business: ['Business Insights', 'Entrepreneur Hub', 'Success Stories', 'Business Growth'],
        health: ['Fitness Flow', 'Wellness Journey', 'Health & Vitality', 'Fit Life'],
        travel: ['Wanderlust Adventures', 'Travel Tales', 'Globe Trotter', 'Journey Diaries'],
        food: ['Culinary Creations', 'Food Adventures', 'Kitchen Chronicles', 'Taste & Tell']
    };
    
    return suggestions[category] || suggestions.tech;
}

function generateDescriptionText(category, name) {
    const templates = {
        tech: `Welcome to ${name}! 🚀\n\nYour go-to source for the latest in technology, reviews, and tutorials. From cutting-edge gadgets to software deep-dives, we cover it all.\n\n📱 What you'll find here:\n• In-depth tech reviews\n• How-to tutorials\n• Industry news and analysis\n• Product comparisons\n\nSubscribe for weekly tech content that matters!`,
        gaming: `Welcome to ${name}! 🎮\n\nJoin me for epic gaming adventures, reviews, and tutorials. From indie gems to AAA blockbusters, we explore it all.\n\n🎯 Content includes:\n• Game reviews and walkthroughs\n• Gaming tips and tricks\n• Live streams and gameplay\n• Industry news\n\nHit subscribe and let's game together!`,
        lifestyle: `Welcome to ${name}! ✨\n\nYour daily dose of lifestyle inspiration, tips, and authentic moments. Join me as I share my journey and discoveries.\n\n💫 You'll find:\n• Daily vlogs and routines\n• Style and beauty tips\n• Life advice and motivation\n• Behind-the-scenes content\n\nSubscribe for positive vibes and real talk!`
    };
    
    return templates[category] || templates.tech.replace('tech', category);
}

function getKeywordSuggestions(category) {
    const keywords = {
        tech: ['technology', 'reviews', 'tutorials', 'gadgets', 'software', 'innovation'],
        gaming: ['gaming', 'gameplay', 'reviews', 'walkthroughs', 'esports', 'streaming'],
        lifestyle: ['lifestyle', 'vlog', 'daily routine', 'inspiration', 'wellness', 'motivation']
    };
    
    return keywords[category] || keywords.tech;
}

function getCompetitorSuggestions(category) {
    const competitors = {
        tech: [
            { name: 'TechCrunch', subscribers: '2.1M', avatar: 'https://via.placeholder.com/50', description: 'Tech news and reviews' },
            { name: 'MKBHD', subscribers: '15.2M', avatar: 'https://via.placeholder.com/50', description: 'Quality tech videos' }
        ],
        gaming: [
            { name: 'PewDiePie', subscribers: '111M', avatar: 'https://via.placeholder.com/50', description: 'Gaming and entertainment' },
            { name: 'Ninja', subscribers: '24M', avatar: 'https://via.placeholder.com/50', description: 'Gaming streams and content' }
        ]
    };
    
    return competitors[category] || competitors.tech;
}

function getDescriptionTemplate(category) {
    const templates = {
        tech: `Welcome to [CHANNEL_NAME]! 🚀

In today's video: [VIDEO_TOPIC]

🔥 What you'll learn:
- [POINT_1]
- [POINT_2] 
- [POINT_3]

⏰ Timestamps:
00:00 - Introduction
[TIMESTAMP_1] - [SECTION_1]
[TIMESTAMP_2] - [SECTION_2]

🔗 Links mentioned:
• [LINK_1]
• [LINK_2]

📱 Connect with me:
• Twitter: [TWITTER]
• Instagram: [INSTAGRAM]
• Website: [WEBSITE]

#Tech #Review #Tutorial #[CATEGORY]`,
        
        gaming: `Welcome to [CHANNEL_NAME]! 🎮

Today's game: [GAME_NAME]

🎯 In this episode:
- [OBJECTIVE_1]
- [OBJECTIVE_2]
- [OBJECTIVE_3]

⏰ Timestamps:
00:00 - Intro
[TIMESTAMP_1] - [SECTION_1]
[TIMESTAMP_2] - [SECTION_2]

🎮 Game info:
• Platform: [PLATFORM]
• Genre: [GENRE]
• Rating: [RATING]

📱 Follow me:
• Twitch: [TWITCH]
• Twitter: [TWITTER]
• Discord: [DISCORD]

#Gaming #[GAME_NAME] #Gameplay #[PLATFORM]`
    };
    
    return templates[category] || templates.tech;
}
