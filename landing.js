// Landing page JavaScript functionality

// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to all links with hash
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Initialize animations and interactions
    initializeAnimations();
    initializeCounters();
    initializeDashboardPreview();
});

// Play demo function
function playDemo() {
    // This would typically open a video modal or redirect to a demo video
    // For now, we'll redirect to the dashboard
    window.open('dashboard.html', '_blank');
}

// Initialize scroll-triggered animations
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .step, .pricing-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Initialize counter animations for hero stats
function initializeCounters() {
    const counters = document.querySelectorAll('.hero-stats .stat strong');
    
    const animateCounter = (element, target, suffix = '') => {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            if (suffix === 'K+') {
                element.textContent = Math.floor(current / 1000) + 'K+';
            } else if (suffix === 'M+') {
                element.textContent = (current / 1000000).toFixed(1) + 'M+';
            } else {
                element.textContent = Math.floor(current) + suffix;
            }
        }, 20);
    };
    
    // Trigger counter animation when hero section is visible
    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Animate counters
                animateCounter(counters[0], 10000, 'K+'); // 10K+
                animateCounter(counters[1], 2000000, 'M+'); // 2M+
                animateCounter(counters[2], 500000000, 'M+'); // 500M+
                
                heroObserver.unobserve(entry.target);
            }
        });
    });
    
    const heroSection = document.querySelector('.hero');
    if (heroSection) {
        heroObserver.observe(heroSection);
    }
}

// Initialize dashboard preview interactions
function initializeDashboardPreview() {
    const preview = document.querySelector('.dashboard-preview');
    
    if (preview) {
        // Add hover effect
        preview.addEventListener('mouseenter', () => {
            preview.style.transform = 'perspective(1000px) rotateY(-2deg) rotateX(2deg) scale(1.02)';
        });
        
        preview.addEventListener('mouseleave', () => {
            preview.style.transform = 'perspective(1000px) rotateY(-5deg) rotateX(5deg) scale(1)';
        });
        
        // Animate chart bars
        const bars = preview.querySelectorAll('.bar');
        bars.forEach((bar, index) => {
            setTimeout(() => {
                bar.style.animation = `growBar 1s ease-out forwards`;
            }, index * 100);
        });
    }
}

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }
});

// Feature card interactions
document.addEventListener('DOMContentLoaded', () => {
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const icon = card.querySelector('.feature-icon');
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        card.addEventListener('mouseleave', () => {
            const icon = card.querySelector('.feature-icon');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });
});

// Pricing card interactions
document.addEventListener('DOMContentLoaded', () => {
    const pricingCards = document.querySelectorAll('.pricing-card');
    
    pricingCards.forEach(card => {
        card.addEventListener('click', () => {
            // Add selection effect
            pricingCards.forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
        });
    });
});

// Form handling (for future contact forms)
function handleFormSubmit(event) {
    event.preventDefault();
    
    // This would typically send data to a server
    // For now, we'll just show a success message
    const form = event.target;
    const formData = new FormData(form);
    
    // Show loading state
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        alert('Thank you for your interest! We\'ll be in touch soon.');
        form.reset();
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }, 1500);
}

// Utility functions
const utils = {
    // Debounce function for performance
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Check if element is in viewport
    isInViewport: (element) => {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },
    
    // Format numbers for display
    formatNumber: (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
};

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .feature-icon {
        transition: transform 0.3s ease;
    }
    
    .pricing-card.selected {
        border-color: #ff0000;
        box-shadow: 0 8px 25px rgba(255, 0, 0, 0.15);
    }
    
    @keyframes growBar {
        from {
            height: 0;
        }
        to {
            height: var(--height, 50%);
        }
    }
`;
document.head.appendChild(style);

// Mobile menu toggle (for future mobile navigation)
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');
    
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

// Track user interactions for analytics (placeholder)
function trackEvent(eventName, properties = {}) {
    // This would typically send data to an analytics service
    console.log('Event tracked:', eventName, properties);
    
    // Example: Track button clicks, form submissions, etc.
    // analytics.track(eventName, properties);
}

// Add event listeners for tracking
document.addEventListener('click', (event) => {
    const target = event.target;
    
    // Track CTA button clicks
    if (target.classList.contains('btn-primary')) {
        trackEvent('CTA Button Clicked', {
            buttonText: target.textContent.trim(),
            location: target.closest('section')?.className || 'unknown'
        });
    }
    
    // Track feature card clicks
    if (target.closest('.feature-card')) {
        const featureTitle = target.closest('.feature-card').querySelector('h3').textContent;
        trackEvent('Feature Card Clicked', {
            feature: featureTitle
        });
    }
});

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('YTFlow Landing Page Loaded');
    
    // Add loading animation
    document.body.classList.add('loaded');
    
    // Initialize tooltips or other UI enhancements
    initializeTooltips();
});

// Initialize tooltips (placeholder for future enhancement)
function initializeTooltips() {
    // This would initialize tooltip library or custom tooltips
    // For now, just add title attributes where needed
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        if (!btn.title && btn.textContent.includes('Demo')) {
            btn.title = 'Try our interactive demo';
        }
    });
}
