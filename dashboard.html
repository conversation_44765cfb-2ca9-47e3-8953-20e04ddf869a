<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YTFlow Dashboard - YouTube Creator Platform</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html">
                    <i class="fab fa-youtube"></i>
                    <span>YTFlow</span>
                </a>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#ideas" class="nav-link">Content Ideas</a>
                <a href="#analytics" class="nav-link">Analytics</a>
                <a href="#competitors" class="nav-link">Competitors</a>
            </div>
            <div class="nav-actions">
                <div class="notifications">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                <div class="user-menu">
                    <button class="user-avatar" onclick="toggleUserMenu()">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="User">
                        <span>Sarah Chen</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#profile"><i class="fas fa-user"></i> Profile</a>
                        <a href="#settings"><i class="fas fa-cog"></i> Settings</a>
                        <a href="#billing"><i class="fas fa-credit-card"></i> Billing</a>
                        <div class="dropdown-divider"></div>
                        <a href="index.html"><i class="fas fa-sign-out-alt"></i> Sign Out</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section (shown when no channels) -->
        <section class="welcome-section" id="welcomeSection">
            <div class="welcome-container">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <h1>Welcome to YTFlow!</h1>
                    <p>Let's get you started by setting up your YouTube channel. Choose how you'd like to begin:</p>

                    <div class="setup-options">
                        <div class="setup-option" onclick="startNewChannelWizard()">
                            <div class="option-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <h3>Create New Channel</h3>
                            <p>Start from scratch with our AI-powered channel setup wizard</p>
                            <ul class="option-features">
                                <li><i class="fas fa-check"></i> AI-generated channel name suggestions</li>
                                <li><i class="fas fa-check"></i> Logo and cover image creation</li>
                                <li><i class="fas fa-check"></i> Competitor analysis setup</li>
                                <li><i class="fas fa-check"></i> Content strategy planning</li>
                            </ul>
                            <button class="btn btn-primary">
                                <i class="fas fa-rocket"></i>
                                Start New Channel
                            </button>
                        </div>

                        <div class="setup-option" onclick="startImportWizard()">
                            <div class="option-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <h3>Import Existing Channel</h3>
                            <p>Connect your existing YouTube channel for instant insights</p>
                            <ul class="option-features">
                                <li><i class="fas fa-check"></i> Automatic data import</li>
                                <li><i class="fas fa-check"></i> Performance analysis</li>
                                <li><i class="fas fa-check"></i> Competitor discovery</li>
                                <li><i class="fas fa-check"></i> Content optimization tips</li>
                            </ul>
                            <button class="btn btn-outline">
                                <i class="fas fa-link"></i>
                                Import Channel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Channel Setup Wizard -->
        <section class="wizard-section" id="wizardSection" style="display: none;">
            <div class="wizard-container">
                <!-- Wizard Header -->
                <div class="wizard-header">
                    <div class="wizard-progress">
                        <div class="progress-step active" data-step="1">
                            <div class="step-number">1</div>
                            <span>Category</span>
                        </div>
                        <div class="progress-step" data-step="2">
                            <div class="step-number">2</div>
                            <span>Region & Language</span>
                        </div>
                        <div class="progress-step" data-step="3">
                            <div class="step-number">3</div>
                            <span>Channel Details</span>
                        </div>
                        <div class="progress-step" data-step="4">
                            <div class="step-number">4</div>
                            <span>Branding</span>
                        </div>
                        <div class="progress-step" data-step="5">
                            <div class="step-number">5</div>
                            <span>Competitors</span>
                        </div>
                        <div class="progress-step" data-step="6">
                            <div class="step-number">6</div>
                            <span>Content Strategy</span>
                        </div>
                    </div>
                </div>

                <!-- Wizard Steps -->
                <div class="wizard-content">
                    <!-- Step 1: Category Selection -->
                    <div class="wizard-step active" data-step="1">
                        <div class="step-content">
                            <h2>Choose Your Content Category</h2>
                            <p>Select the primary category that best describes your channel content. This helps us provide better recommendations.</p>

                            <div class="category-grid">
                                <div class="category-card" data-category="tech">
                                    <div class="category-icon">
                                        <i class="fas fa-microchip"></i>
                                    </div>
                                    <h3>Technology</h3>
                                    <p>Tech reviews, tutorials, and industry news</p>
                                </div>

                                <div class="category-card" data-category="gaming">
                                    <div class="category-icon">
                                        <i class="fas fa-gamepad"></i>
                                    </div>
                                    <h3>Gaming</h3>
                                    <p>Game reviews, walkthroughs, and streaming</p>
                                </div>

                                <div class="category-card" data-category="lifestyle">
                                    <div class="category-icon">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <h3>Lifestyle</h3>
                                    <p>Vlogs, fashion, beauty, and daily life</p>
                                </div>

                                <div class="category-card" data-category="education">
                                    <div class="category-icon">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h3>Education</h3>
                                    <p>Tutorials, courses, and educational content</p>
                                </div>

                                <div class="category-card" data-category="entertainment">
                                    <div class="category-icon">
                                        <i class="fas fa-theater-masks"></i>
                                    </div>
                                    <h3>Entertainment</h3>
                                    <p>Comedy, music, movies, and shows</p>
                                </div>

                                <div class="category-card" data-category="business">
                                    <div class="category-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <h3>Business</h3>
                                    <p>Entrepreneurship, finance, and marketing</p>
                                </div>

                                <div class="category-card" data-category="health">
                                    <div class="category-icon">
                                        <i class="fas fa-dumbbell"></i>
                                    </div>
                                    <h3>Health & Fitness</h3>
                                    <p>Workouts, nutrition, and wellness</p>
                                </div>

                                <div class="category-card" data-category="travel">
                                    <div class="category-icon">
                                        <i class="fas fa-plane"></i>
                                    </div>
                                    <h3>Travel</h3>
                                    <p>Travel vlogs, guides, and adventures</p>
                                </div>

                                <div class="category-card" data-category="food">
                                    <div class="category-icon">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <h3>Food & Cooking</h3>
                                    <p>Recipes, restaurant reviews, and cooking tips</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Region & Language -->
                    <div class="wizard-step" data-step="2">
                        <div class="step-content">
                            <h2>Region & Language Settings</h2>
                            <p>Help us understand your target audience and provide localized recommendations.</p>

                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="region">Primary Region</label>
                                    <select id="region" name="region">
                                        <option value="">Select your primary region</option>
                                        <option value="US">United States</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="CA">Canada</option>
                                        <option value="AU">Australia</option>
                                        <option value="DE">Germany</option>
                                        <option value="FR">France</option>
                                        <option value="ES">Spain</option>
                                        <option value="IT">Italy</option>
                                        <option value="JP">Japan</option>
                                        <option value="KR">South Korea</option>
                                        <option value="IN">India</option>
                                        <option value="BR">Brazil</option>
                                        <option value="MX">Mexico</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="language">Content Language</label>
                                    <select id="language" name="language">
                                        <option value="">Select content language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="hi">Hindi</option>
                                        <option value="ar">Arabic</option>
                                        <option value="ru">Russian</option>
                                    </select>
                                </div>
                            </div>

                            <div class="timezone-section">
                                <h3>Optimal Posting Times</h3>
                                <p>Based on your region, we recommend posting during these peak hours:</p>
                                <div class="timezone-info">
                                    <div class="time-slot">
                                        <i class="fas fa-clock"></i>
                                        <span>Weekdays: 2-4 PM local time</span>
                                    </div>
                                    <div class="time-slot">
                                        <i class="fas fa-clock"></i>
                                        <span>Weekends: 9-11 AM local time</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Step 3: Channel Details -->
                    <div class="wizard-step" data-step="3">
                        <div class="step-content">
                            <h2>Channel Details</h2>
                            <p>Let's create your channel identity. Our AI will help generate suggestions based on your category.</p>

                            <div class="form-group">
                                <label for="channelName">Channel Name</label>
                                <div class="input-with-ai">
                                    <input type="text" id="channelName" placeholder="Enter your channel name">
                                    <button class="ai-suggest-btn" onclick="generateChannelNames()">
                                        <i class="fas fa-magic"></i>
                                        AI Suggest
                                    </button>
                                </div>
                                <div class="ai-suggestions" id="channelNameSuggestions"></div>
                            </div>

                            <div class="form-group">
                                <label for="channelDescription">Channel Description</label>
                                <div class="input-with-ai">
                                    <textarea id="channelDescription" rows="4" placeholder="Describe what your channel is about..."></textarea>
                                    <button class="ai-suggest-btn" onclick="generateChannelDescription()">
                                        <i class="fas fa-magic"></i>
                                        AI Generate
                                    </button>
                                </div>
                                <div class="character-count">
                                    <span id="descriptionCount">0</span>/1000 characters
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channelKeywords">Channel Keywords</label>
                                <div class="input-with-ai">
                                    <input type="text" id="channelKeywords" placeholder="Enter keywords separated by commas">
                                    <button class="ai-suggest-btn" onclick="generateKeywords()">
                                        <i class="fas fa-magic"></i>
                                        AI Suggest
                                    </button>
                                </div>
                                <div class="ai-suggestions" id="keywordSuggestions"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Branding -->
                    <div class="wizard-step" data-step="4">
                        <div class="step-content">
                            <h2>Channel Branding</h2>
                            <p>Create a professional look for your channel with AI-generated logos and cover images.</p>

                            <div class="branding-section">
                                <div class="branding-item">
                                    <h3>Channel Logo</h3>
                                    <div class="logo-creator">
                                        <div class="logo-preview">
                                            <div class="logo-placeholder" id="logoPreview">
                                                <i class="fas fa-image"></i>
                                                <span>Logo Preview</span>
                                            </div>
                                        </div>
                                        <div class="logo-controls">
                                            <button class="btn btn-outline" onclick="uploadLogo()">
                                                <i class="fas fa-upload"></i>
                                                Upload Logo
                                            </button>
                                            <button class="btn btn-primary" onclick="generateLogo()">
                                                <i class="fas fa-magic"></i>
                                                AI Generate Logo
                                            </button>
                                        </div>
                                        <input type="file" id="logoUpload" accept="image/*" style="display: none;">
                                    </div>
                                </div>

                                <div class="branding-item">
                                    <h3>Channel Cover Image</h3>
                                    <div class="cover-creator">
                                        <div class="cover-preview">
                                            <div class="cover-placeholder" id="coverPreview">
                                                <i class="fas fa-image"></i>
                                                <span>Cover Image Preview (2560x1440)</span>
                                            </div>
                                        </div>
                                        <div class="cover-controls">
                                            <button class="btn btn-outline" onclick="uploadCover()">
                                                <i class="fas fa-upload"></i>
                                                Upload Cover
                                            </button>
                                            <button class="btn btn-primary" onclick="generateCover()">
                                                <i class="fas fa-magic"></i>
                                                AI Generate Cover
                                            </button>
                                        </div>
                                        <input type="file" id="coverUpload" accept="image/*" style="display: none;">
                                    </div>
                                </div>
                            </div>

                            <div class="color-scheme">
                                <h3>Brand Colors</h3>
                                <div class="color-palette">
                                    <div class="color-option" data-color="#ff0000" style="background: #ff0000;"></div>
                                    <div class="color-option" data-color="#00ff00" style="background: #00ff00;"></div>
                                    <div class="color-option" data-color="#0066ff" style="background: #0066ff;"></div>
                                    <div class="color-option" data-color="#ff6600" style="background: #ff6600;"></div>
                                    <div class="color-option" data-color="#9900ff" style="background: #9900ff;"></div>
                                    <div class="color-option" data-color="#ff0099" style="background: #ff0099;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Competitors -->
                    <div class="wizard-step" data-step="5">
                        <div class="step-content">
                            <h2>Competitor Analysis</h2>
                            <p>Identify your main competitors to understand your niche and find content opportunities.</p>

                            <div class="competitor-search">
                                <div class="search-box">
                                    <input type="text" id="competitorSearch" placeholder="Search for competitor channels...">
                                    <button class="search-btn" onclick="searchCompetitors()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <button class="ai-suggest-btn" onclick="suggestCompetitors()">
                                    <i class="fas fa-magic"></i>
                                    AI Suggest Competitors
                                </button>
                            </div>

                            <div class="competitor-results" id="competitorResults">
                                <!-- AI suggested competitors will appear here -->
                            </div>

                            <div class="selected-competitors">
                                <h3>Selected Competitors</h3>
                                <div class="competitor-list" id="selectedCompetitors">
                                    <div class="empty-state">
                                        <i class="fas fa-users"></i>
                                        <p>No competitors selected yet. Use the search above or AI suggestions.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Content Strategy -->
                    <div class="wizard-step" data-step="6">
                        <div class="step-content">
                            <h2>Content Strategy</h2>
                            <p>Set up your content templates and strategy for consistent, engaging videos.</p>

                            <div class="strategy-section">
                                <div class="template-creator">
                                    <h3>Video Description Template</h3>
                                    <div class="template-editor">
                                        <textarea id="descriptionTemplate" rows="8" placeholder="Create your video description template...">
Welcome to [CHANNEL_NAME]!

In today's video: [VIDEO_TOPIC]

🎯 What you'll learn:
- [POINT_1]
- [POINT_2]
- [POINT_3]

⏰ Timestamps:
00:00 - Introduction
[TIMESTAMP_1] - [SECTION_1]
[TIMESTAMP_2] - [SECTION_2]

📱 Connect with me:
• Instagram: [INSTAGRAM]
• Twitter: [TWITTER]
• Website: [WEBSITE]

#[TAG_1] #[TAG_2] #[TAG_3]
                                        </textarea>
                                        <button class="ai-suggest-btn" onclick="generateDescriptionTemplate()">
                                            <i class="fas fa-magic"></i>
                                            AI Generate Template
                                        </button>
                                    </div>
                                </div>

                                <div class="posting-schedule">
                                    <h3>Posting Schedule</h3>
                                    <div class="schedule-grid">
                                        <div class="schedule-day">
                                            <label>
                                                <input type="checkbox" name="schedule" value="monday">
                                                <span>Monday</span>
                                            </label>
                                            <select name="time-monday">
                                                <option value="09:00">9:00 AM</option>
                                                <option value="12:00">12:00 PM</option>
                                                <option value="15:00">3:00 PM</option>
                                                <option value="18:00">6:00 PM</option>
                                            </select>
                                        </div>
                                        <div class="schedule-day">
                                            <label>
                                                <input type="checkbox" name="schedule" value="wednesday">
                                                <span>Wednesday</span>
                                            </label>
                                            <select name="time-wednesday">
                                                <option value="09:00">9:00 AM</option>
                                                <option value="12:00">12:00 PM</option>
                                                <option value="15:00">3:00 PM</option>
                                                <option value="18:00">6:00 PM</option>
                                            </select>
                                        </div>
                                        <div class="schedule-day">
                                            <label>
                                                <input type="checkbox" name="schedule" value="friday">
                                                <span>Friday</span>
                                            </label>
                                            <select name="time-friday">
                                                <option value="09:00">9:00 AM</option>
                                                <option value="12:00">12:00 PM</option>
                                                <option value="15:00">3:00 PM</option>
                                                <option value="18:00">6:00 PM</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wizard Navigation -->
                <div class="wizard-navigation">
                    <button class="btn btn-outline" id="prevBtn" onclick="previousStep()" style="display: none;">
                        <i class="fas fa-arrow-left"></i>
                        Previous
                    </button>
                    <button class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                        Next
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <button class="btn btn-primary" id="finishBtn" onclick="finishSetup()" style="display: none;">
                        <i class="fas fa-check"></i>
                        Complete Setup
                    </button>
                </div>
            </div>
        </section>

        <!-- Import Channel Section -->
        <section class="import-section" id="importSection" style="display: none;">
            <div class="import-container">
                <div class="import-header">
                    <h2>Import Existing Channel</h2>
                    <p>Connect your YouTube channel to get instant insights and recommendations.</p>
                </div>

                <div class="import-methods">
                    <div class="import-method">
                        <h3>Import by Channel URL</h3>
                        <div class="url-input">
                            <input type="url" id="channelUrl" placeholder="https://youtube.com/@yourchannel">
                            <button class="btn btn-primary" onclick="importByUrl()">
                                <i class="fas fa-link"></i>
                                Import Channel
                            </button>
                        </div>
                    </div>

                    <div class="import-divider">
                        <span>or</span>
                    </div>

                    <div class="import-method">
                        <h3>Connect with YouTube</h3>
                        <button class="btn btn-youtube" onclick="connectYouTube()">
                            <i class="fab fa-youtube"></i>
                            Connect YouTube Account
                        </button>
                        <p class="import-note">We'll securely access your channel data to provide personalized insights.</p>
                    </div>
                </div>

                <div class="import-back">
                    <button class="btn btn-outline" onclick="showWelcome()">
                        <i class="fas fa-arrow-left"></i>
                        Back to Options
                    </button>
                </div>
            </div>
        </section>

        <!-- Dashboard Content (shown after setup) -->
        <section class="dashboard-content" id="dashboardContent" style="display: none;">
            <!-- This will be populated after channel setup -->
        </section>
    </main>
    <script src="dashboard.js"></script>
</body>
</html>
