// Sample headlines data for different content types
const headlineTemplates = {
    'Tech Review': [
        'The TRUTH About {product} - Honest Review After 30 Days',
        'Why {product} is BETTER Than Everyone Says (Detailed Analysis)',
        '{product} vs {competitor} - Which One Should You Buy in 2024?',
        'I Used {product} for a Month - Here\'s What Happened',
        'The {product} Review Nobody Asked For (But Everyone Needs)',
        '{product} Unboxing & First Impressions - Worth the Hype?',
        'Why I\'m Switching to {product} (And You Should Too)',
        'The Complete {product} Buyer\'s Guide - Everything You Need to Know'
    ],
    'Tutorial': [
        'Complete Beginner\'s Guide to {topic} in 2024',
        'How to {skill} Like a Pro - Step by Step Tutorial',
        'Master {topic} in 30 Minutes (No Experience Required)',
        'The ONLY {topic} Tutorial You\'ll Ever Need',
        '{topic} Tutorial - From Zero to Hero in One Video',
        'Learn {topic} the RIGHT Way - Avoid These Common Mistakes',
        'How I Learned {topic} in 30 Days (You Can Too)',
        'The Ultimate {topic} Crash Course for Beginners'
    ],
    'Lifestyle': [
        'How {topic} Changed My Life (And Can Change Yours)',
        'The {topic} Routine That Actually Works',
        'Why Everyone is Wrong About {topic}',
        'My {topic} Journey - What I Wish I Knew Earlier',
        'The Science Behind {topic} - Does It Really Work?',
        '{topic} for Beginners - Start Here',
        'How to {topic} Without Breaking the Bank',
        'The {topic} Mistakes Everyone Makes (And How to Avoid Them)'
    ]
};

// Function to generate headlines when idea card is clicked
function generateHeadline(cardElement) {
    const category = cardElement.querySelector('.idea-category').textContent;
    const title = cardElement.querySelector('h3').textContent;
    
    // Extract key terms from the title for personalization
    const keyTerms = extractKeyTerms(title);
    
    // Get appropriate templates
    const templates = headlineTemplates[category] || headlineTemplates['Tutorial'];
    
    // Generate personalized headlines
    const headlines = generatePersonalizedHeadlines(templates, keyTerms, title);
    
    // Show modal with generated headlines
    showHeadlineModal(headlines, category);
}

// Extract key terms from title for headline personalization
function extractKeyTerms(title) {
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'how', 'what', 'why', 'when', 'where'];
    const words = title.toLowerCase().split(' ').filter(word => 
        word.length > 2 && !commonWords.includes(word)
    );
    return words.slice(0, 3); // Take first 3 relevant words
}

// Generate personalized headlines based on templates and key terms
function generatePersonalizedHeadlines(templates, keyTerms, originalTitle) {
    const headlines = [];
    
    templates.forEach(template => {
        let headline = template;
        
        // Replace placeholders with actual terms
        if (headline.includes('{product}')) {
            headline = headline.replace('{product}', keyTerms[0] || 'Product');
        }
        if (headline.includes('{topic}')) {
            headline = headline.replace('{topic}', keyTerms[0] || 'Topic');
        }
        if (headline.includes('{skill}')) {
            headline = headline.replace('{skill}', keyTerms[0] || 'Skill');
        }
        if (headline.includes('{competitor}')) {
            headline = headline.replace('{competitor}', keyTerms[1] || 'Alternative');
        }
        
        headlines.push({
            title: headline,
            engagement: Math.floor(Math.random() * 30) + 70, // Random engagement score 70-100
            seoScore: Math.floor(Math.random() * 20) + 80 // Random SEO score 80-100
        });
    });
    
    // Add some variations of the original title
    headlines.push(
        {
            title: `${originalTitle} - Complete Guide`,
            engagement: Math.floor(Math.random() * 15) + 75,
            seoScore: Math.floor(Math.random() * 15) + 80
        },
        {
            title: `Everything You Need to Know About ${originalTitle}`,
            engagement: Math.floor(Math.random() * 15) + 70,
            seoScore: Math.floor(Math.random() * 15) + 75
        }
    );
    
    return headlines.slice(0, 8); // Return top 8 headlines
}

// Show modal with generated headlines
function showHeadlineModal(headlines, category) {
    const modal = document.getElementById('headlineModal');
    const headlinesList = document.getElementById('headlinesList');
    
    // Clear previous headlines
    headlinesList.innerHTML = '';
    
    // Add new headlines
    headlines.forEach(headline => {
        const headlineElement = document.createElement('div');
        headlineElement.className = 'headline-item';
        headlineElement.innerHTML = `
            <h4>${headline.title}</h4>
            <p>
                <span style="color: #10b981;">Engagement Score: ${headline.engagement}%</span> • 
                <span style="color: #3b82f6;">SEO Score: ${headline.seoScore}%</span>
            </p>
        `;
        
        // Add click handler to copy headline
        headlineElement.addEventListener('click', () => {
            navigator.clipboard.writeText(headline.title).then(() => {
                // Show temporary feedback
                const originalContent = headlineElement.innerHTML;
                headlineElement.innerHTML = `
                    <h4 style="color: #10b981;">${headline.title} ✓ Copied!</h4>
                    <p>
                        <span style="color: #10b981;">Engagement Score: ${headline.engagement}%</span> • 
                        <span style="color: #3b82f6;">SEO Score: ${headline.seoScore}%</span>
                    </p>
                `;
                setTimeout(() => {
                    headlineElement.innerHTML = originalContent;
                }, 1500);
            });
        });
        
        headlinesList.appendChild(headlineElement);
    });
    
    modal.style.display = 'block';
}

// Close modal
function closeModal() {
    document.getElementById('headlineModal').style.display = 'none';
}

// Generate more headlines
function generateMoreHeadlines() {
    // This would typically make an API call to generate more headlines
    // For demo purposes, we'll just show a message
    alert('🤖 AI is generating more headlines... This would connect to your AI service!');
}

// Close modal when clicking outside
window.addEventListener('click', (event) => {
    const modal = document.getElementById('headlineModal');
    if (event.target === modal) {
        closeModal();
    }
});

// Simulate real-time updates for stats
function updateStats() {
    const statElements = document.querySelectorAll('.stat-content h3');
    statElements.forEach(element => {
        const currentValue = element.textContent;
        // Add subtle animation or update effect
        element.style.transform = 'scale(1.05)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 200);
    });
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('YTFlow - YouTube Creator Platform Loaded');
    
    // Add some interactive behaviors
    const ideaCards = document.querySelectorAll('.idea-card');
    ideaCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
        });
    });
    
    // Simulate periodic stats updates
    setInterval(updateStats, 30000); // Update every 30 seconds
});

// Additional utility functions for future features
const YTFlowUtils = {
    // Format numbers for display
    formatNumber: (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },
    
    // Calculate engagement rate
    calculateEngagement: (likes, comments, views) => {
        return ((likes + comments) / views * 100).toFixed(2);
    },
    
    // Generate color based on performance
    getPerformanceColor: (score) => {
        if (score >= 80) return '#10b981'; // Green
        if (score >= 60) return '#fbbf24'; // Yellow
        return '#ef4444'; // Red
    }
};
