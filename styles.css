/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #fafafa;
    color: #1a1a1a;
    line-height: 1.6;
}

/* Navigation */
.navbar {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    height: 64px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff0000;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.nav-link:hover,
.nav-link.active {
    color: #1a1a1a;
    background-color: #f5f5f5;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.btn-primary {
    background-color: #1a1a1a;
    color: white;
}

.btn-primary:hover {
    background-color: #333;
}

.btn-outline {
    background-color: transparent;
    color: #666;
    border: 1px solid #e5e5e5;
}

.btn-outline:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Main Layout */
.main-content {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

/* Sidebar */
.sidebar {
    width: 280px;
    flex-shrink: 0;
}

.sidebar-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e5e5e5;
}

.sidebar-section h3 {
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: transparent;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    text-align: left;
    width: 100%;
}

.action-btn:hover {
    background-color: #f5f5f5;
}

.recent-projects {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.project-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.project-item:hover {
    background-color: #f5f5f5;
}

.project-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-in-progress { background-color: #fbbf24; }
.status-completed { background-color: #10b981; }
.status-planning { background-color: #6b7280; }

/* Dashboard */
.dashboard {
    flex: 1;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #666;
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change.positive { color: #10b981; }
.stat-change.negative { color: #ef4444; }
.stat-change.neutral { color: #6b7280; }

/* Content Sections */
.content-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e5e5e5;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Ideas Grid */
.ideas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.idea-card {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.idea-card:hover {
    border-color: #d0d0d0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.idea-category {
    display: inline-block;
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.idea-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.idea-card p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.idea-metrics {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #666;
}

.idea-actions {
    display: flex;
    gap: 0.75rem;
}

/* Competitors */
.competitors-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.competitor-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
}

.competitor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.competitor-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.competitor-info h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.competitor-info p {
    color: #666;
    font-size: 0.875rem;
}

.competitor-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat {
    font-size: 0.875rem;
    color: #666;
}

.trend-up { color: #10b981; }
.trend-down { color: #ef4444; }

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    margin: 5% auto;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e5e5;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1.5rem;
}

.headlines-list {
    margin-bottom: 1.5rem;
}

.headline-item {
    padding: 1rem;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.headline-item:hover {
    border-color: #1a1a1a;
    background-color: #f9f9f9;
}

.headline-item h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.headline-item p {
    color: #666;
    font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ideas-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
}
