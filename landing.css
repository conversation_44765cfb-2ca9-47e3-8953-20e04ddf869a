/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #ffffff;
    color: #1a1a1a;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff0000;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.nav-link:hover {
    color: #1a1a1a;
    background-color: #f5f5f5;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.btn-outline {
    background-color: transparent;
    color: #666;
    border: 2px solid #e5e5e5;
}

.btn-outline:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
    color: #1a1a1a;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 0, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 0, 0, 0.1);
    color: #ff0000;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
}

.gradient-text {
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.hero-stats .stat {
    text-align: center;
}

.hero-stats .stat strong {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
}

.hero-stats .stat span {
    color: #666;
    font-size: 0.875rem;
}

/* Dashboard Preview */
.hero-visual {
    position: relative;
}

.dashboard-preview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(-2deg) rotateX(2deg);
}

.preview-header {
    background: #f8f9fa;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #e5e5e5;
}

.preview-dots {
    display: flex;
    gap: 0.5rem;
}

.preview-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
}

.preview-dots span:nth-child(1) { background: #ff5f57; }
.preview-dots span:nth-child(2) { background: #ffbd2e; }
.preview-dots span:nth-child(3) { background: #28ca42; }

.preview-title {
    font-weight: 600;
    color: #666;
    font-size: 0.875rem;
}

.preview-content {
    padding: 1.5rem;
}

.preview-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.preview-stat {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    flex: 1;
}

.preview-stat .stat-icon {
    width: 32px;
    height: 32px;
    background: #e5e5e5;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.preview-stat .stat-info strong {
    display: block;
    font-weight: 700;
    color: #1a1a1a;
}

.preview-stat .stat-info span {
    font-size: 0.75rem;
    color: #666;
}

.preview-chart {
    margin-bottom: 1.5rem;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 60px;
}

.bar {
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    border-radius: 2px;
    flex: 1;
    min-height: 10px;
    animation: growBar 1s ease-out;
}

@keyframes growBar {
    from { height: 0; }
    to { height: var(--height); }
}

.preview-ideas {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.idea-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.idea-category {
    background: #e5e5e5;
    color: #666;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.idea-title {
    font-weight: 600;
    color: #1a1a1a;
    font-size: 0.875rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.section-header p {
    font-size: 1.125rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #fafafa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.feature-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    color: #666;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: 700;
}

/* How It Works Section */
.how-it-works {
    padding: 80px 0;
    background: white;
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.step h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.step p {
    color: #666;
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
    background: #fafafa;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: all 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured {
    border: 2px solid #ff0000;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.currency {
    font-size: 1.25rem;
    color: #666;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #1a1a1a;
}

.period {
    font-size: 1rem;
    color: #666;
}

.plan-header p {
    color: #666;
}

.plan-features {
    list-style: none;
    margin-bottom: 2rem;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #666;
}

.plan-features li i {
    color: #10b981;
    font-size: 0.875rem;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #333 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-actions {
    margin-bottom: 1rem;
}

.cta-note {
    font-size: 0.875rem;
    opacity: 0.7;
    margin-top: 1rem;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand p {
    color: #ccc;
    margin-top: 1rem;
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.link-group h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.link-group a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.2s;
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #ccc;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    color: #ccc;
    font-size: 1.25rem;
    transition: color 0.2s;
}

.footer-social a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .nav-menu {
        display: none;
    }
    
    .features-grid,
    .steps-container,
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
