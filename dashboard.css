/* Dashboard Styles - Modern Design System */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    color: #1a1a1a;
    line-height: 1.6;
    padding-top: 70px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    padding: 0 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    height: 100%;
}

.nav-brand a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff0000;
    text-decoration: none;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.nav-link:hover,
.nav-link.active {
    color: #1a1a1a;
    background-color: #f5f5f5;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notifications {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s;
    position: relative;
}

.notification-btn:hover {
    background: #f5f5f5;
    color: #1a1a1a;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #ff0000;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s;
}

.user-avatar:hover {
    background: #f5f5f5;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-avatar span {
    font-weight: 500;
    color: #1a1a1a;
}

.user-avatar i {
    color: #666;
    font-size: 0.875rem;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    padding: 0.5rem 0;
    display: none;
    z-index: 1000;
}

.user-dropdown.show {
    display: block;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    transition: all 0.2s;
}

.user-dropdown a:hover {
    background: #f5f5f5;
    color: #1a1a1a;
}

.dropdown-divider {
    height: 1px;
    background: #e5e5e5;
    margin: 0.5rem 0;
}

/* Main Content */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Welcome Section */
.welcome-section {
    min-height: calc(100vh - 140px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-container {
    text-align: center;
    max-width: 1200px;
    width: 100%;
}

.welcome-content {
    background: white;
    border-radius: 16px;
    padding: 4rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e5e5;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
}

.welcome-content h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.welcome-content p {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.setup-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.setup-option {
    background: #fafafa;
    border: 2px solid #e5e5e5;
    border-radius: 16px;
    padding: 2.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.setup-option:hover {
    border-color: #ff0000;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 0, 0, 0.1);
}

.option-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.setup-option h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.setup-option p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.option-features {
    list-style: none;
    margin-bottom: 2rem;
}

.option-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #666;
    font-size: 0.875rem;
}

.option-features i {
    color: #10b981;
    font-size: 0.875rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4);
}

.btn-outline {
    background: white;
    color: #666;
    border: 2px solid #e5e5e5;
}

.btn-outline:hover {
    border-color: #ff0000;
    color: #ff0000;
}

.btn-youtube {
    background: #ff0000;
    color: white;
}

.btn-youtube:hover {
    background: #cc0000;
}

/* Wizard Section */
.wizard-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e5e5;
    overflow: hidden;
}

.wizard-header {
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
    padding: 2rem;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    position: relative;
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: #e5e5e5;
    z-index: 0;
}

.progress-step.active:not(:last-child)::after,
.progress-step.completed:not(:last-child)::after {
    background: #ff0000;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e5e5;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;
    z-index: 1;
    transition: all 0.3s;
}

.progress-step.active .step-number {
    background: #ff0000;
    color: white;
}

.progress-step.completed .step-number {
    background: #10b981;
    color: white;
}

.progress-step span {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
    text-align: center;
}

.progress-step.active span {
    color: #1a1a1a;
    font-weight: 600;
}

/* Wizard Content */
.wizard-content {
    padding: 3rem;
}

.wizard-step {
    display: none;
}

.wizard-step.active {
    display: block;
}

.step-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.step-content p {
    font-size: 1.125rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
}

.category-card {
    background: #fafafa;
    border: 2px solid #e5e5e5;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover,
.category-card.selected {
    border-color: #ff0000;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.1);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
}

.category-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
}

.category-card p {
    color: #666;
    font-size: 0.875rem;
    margin: 0;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff0000;
    box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

.input-with-ai {
    position: relative;
    display: flex;
    gap: 0.5rem;
}

.input-with-ai input,
.input-with-ai textarea {
    flex: 1;
}

.ai-suggest-btn {
    background: linear-gradient(135deg, #ff0000 0%, #ff6b6b 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.ai-suggest-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
}

.ai-suggestions {
    margin-top: 1rem;
    display: none;
}

.ai-suggestions.show {
    display: block;
}

.suggestion-item {
    background: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.suggestion-item:hover {
    background: #e9ecef;
    border-color: #ff0000;
}

.character-count {
    text-align: right;
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Timezone Section */
.timezone-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin-top: 2rem;
}

.timezone-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.timezone-info {
    display: flex;
    gap: 2rem;
}

.time-slot {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #666;
}

.time-slot i {
    color: #ff0000;
}

/* Branding Section */
.branding-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.branding-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #1a1a1a;
}

.logo-preview,
.cover-preview {
    background: #f8f9fa;
    border: 2px dashed #e5e5e5;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all 0.2s;
}

.logo-preview {
    width: 200px;
    height: 200px;
    margin: 0 auto 1rem;
}

.cover-preview {
    width: 100%;
    height: 150px;
}

.logo-placeholder,
.cover-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.logo-placeholder i,
.cover-placeholder i {
    font-size: 2rem;
}

.logo-controls,
.cover-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.color-scheme {
    margin-top: 2rem;
}

.color-scheme h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.color-palette {
    display: flex;
    gap: 1rem;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.2s;
}

.color-option:hover,
.color-option.selected {
    border-color: #1a1a1a;
    transform: scale(1.1);
}

/* Competitor Section */
.competitor-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    font-size: 1rem;
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: #ff0000;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
}

.competitor-results {
    margin-bottom: 2rem;
}

.competitor-card {
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s;
}

.competitor-card:hover {
    border-color: #ff0000;
    box-shadow: 0 2px 8px rgba(255, 0, 0, 0.1);
}

.competitor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.competitor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.competitor-details h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.competitor-details p {
    color: #666;
    font-size: 0.875rem;
    margin: 0;
}

.add-competitor-btn {
    background: #10b981;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-weight: 500;
}

.selected-competitors h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.competitor-list {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 100px;
}

.empty-state {
    text-align: center;
    color: #666;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #ccc;
}

/* Strategy Section */
.strategy-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.template-creator h3,
.posting-schedule h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.template-editor {
    position: relative;
}

.template-editor textarea {
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
}

.schedule-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.schedule-day {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.schedule-day label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.schedule-day input[type="checkbox"] {
    width: auto;
}

.schedule-day select {
    width: auto;
    min-width: 120px;
}

/* Wizard Navigation */
.wizard-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 3rem;
    background: #fafafa;
    border-top: 1px solid #e5e5e5;
}

/* Import Section */
.import-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e5e5;
    padding: 3rem;
    text-align: center;
}

.import-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.import-header p {
    font-size: 1.125rem;
    color: #666;
    margin-bottom: 3rem;
}

.import-methods {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.import-method h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.url-input {
    display: flex;
    gap: 1rem;
}

.url-input input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    font-size: 1rem;
}

.import-divider {
    position: relative;
    text-align: center;
    color: #666;
    margin: 2rem 0;
}

.import-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e5e5;
}

.import-divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
}

.import-note {
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.import-back {
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .welcome-content {
        padding: 2rem;
    }
    
    .welcome-content h1 {
        font-size: 2rem;
    }
    
    .setup-options {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .wizard-progress {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .progress-step:not(:last-child)::after {
        display: none;
    }
    
    .wizard-content {
        padding: 2rem 1rem;
    }
    
    .category-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .branding-section {
        grid-template-columns: 1fr;
    }
    
    .strategy-section {
        grid-template-columns: 1fr;
    }
    
    .wizard-navigation {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .user-avatar span {
        display: none;
    }
}
